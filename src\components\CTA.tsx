import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { ArrowRight, CheckCircle, Clock, Users, Zap, Send, Mail, Phone } from "lucide-react";

const CTA = () => {
  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        {/* Main Headline */}
        <div className="text-center mb-16 space-y-4">
          <Badge variant="secondary" className="bg-primary/10 text-primary mb-4">
            <Zap className="w-4 h-4 mr-2" />
            Jetzt starten
          </Badge>
          <h2 className="text-3xl lg:text-5xl font-bold text-foreground">
            Bereit für die Zukunft der
            <span className="block text-primary">Energieberatung?</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Starten Sie noch heute mit SmartEnergy und erleben Sie, 
            wie einfach moderne Energieberatung sein kann.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          
          {/* Quick Start Options */}
          <Card className="bg-gradient-card border-border shadow-glow">
            <CardContent className="p-8 space-y-6">
              <div className="text-center space-y-4">
                <h3 className="text-2xl font-bold text-foreground">Sofort loslegen</h3>
                <p className="text-muted-foreground">
                  Testen Sie SmartEnergy oder vereinbaren Sie eine persönliche Demo
                </p>
              </div>

              {/* Benefits */}
              <div className="grid grid-cols-1 gap-3">
                <div className="flex items-center space-x-3 p-3 rounded-lg bg-secondary/50">
                  <CheckCircle className="w-4 h-4 text-primary flex-shrink-0" />
                  <span className="text-sm font-medium text-foreground">14 Tage kostenlos testen</span>
                </div>
                <div className="flex items-center space-x-3 p-3 rounded-lg bg-secondary/50">
                  <Clock className="w-4 h-4 text-primary flex-shrink-0" />
                  <span className="text-sm font-medium text-foreground">In 2 Minuten startklar</span>
                </div>
                <div className="flex items-center space-x-3 p-3 rounded-lg bg-secondary/50">
                  <Users className="w-4 h-4 text-primary flex-shrink-0" />
                  <span className="text-sm font-medium text-foreground">Persönlicher Support</span>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="space-y-3">
                <Button size="lg" variant="outline" className="w-full">
                  <ArrowRight className="w-5 h-5 mr-2" />
                  Kostenlos testen
                </Button>
                <Button size="lg" variant="outline" className="w-full">
                  <Phone className="w-5 h-5 mr-2" />
                  Demo vereinbaren
                </Button>
              </div>

              {/* Trust */}
              <div className="text-center text-sm text-muted-foreground space-y-2">
                <div>Bereits von über 500+ Energieberatern vertraut</div>
                <div className="flex items-center justify-center space-x-4 text-xs">
                  <span>✓ Keine Kreditkarte erforderlich</span>
                  <span>✓ Jederzeit kündbar</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Form */}
          <Card className="bg-gradient-card border-border shadow-glow">
            <CardContent className="p-8 space-y-6">
              <div className="text-center space-y-4">
                <Mail className="w-12 h-12 text-primary mx-auto" />
                <h3 className="text-2xl font-bold text-foreground">Kontakt aufnehmen</h3>
                <p className="text-muted-foreground">
                  Haben Sie Fragen? Schreiben Sie uns eine Nachricht und wir melden uns schnellstmöglich zurück.
                </p>
              </div>

              <form className="space-y-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">Vorname *</Label>
                    <Input id="firstName" placeholder="Ihr Vorname" required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Nachname *</Label>
                    <Input id="lastName" placeholder="Ihr Nachname" required />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="email">E-Mail *</Label>
                  <Input id="email" type="email" placeholder="<EMAIL>" required />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="phone">Telefon</Label>
                  <Input id="phone" type="tel" placeholder="Ihre Telefonnummer (optional)" />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="company">Unternehmen</Label>
                  <Input id="company" placeholder="Ihr Unternehmen (optional)" />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="message">Nachricht *</Label>
                  <Textarea 
                    id="message" 
                    placeholder="Beschreiben Sie kurz Ihr Anliegen oder Ihre Fragen..."
                    className="min-h-[100px]"
                    required
                  />
                </div>

                <Button type="submit" size="lg" className="w-full bg-primary hover:bg-primary/90">
                  <Send className="w-5 h-5 mr-2" />
                  Nachricht senden
                </Button>
              </form>

              <div className="text-center text-xs text-muted-foreground">
                ✓ DSGVO-konform • Ihre Daten werden vertraulich behandelt
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default CTA;