import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious, CarouselApi } from "@/components/ui/carousel";
import { useEffect, useState } from "react";
const videoData = [{
  id: "BXyh0YNC414",
  title: "Plattform-Überblick",
  description: "Lernen Sie die Grundlagen unserer SmartEnergy-Plattform kennen"
}, {
  id: "4zTZUnceOYM",
  title: "Dashboard-Navigation",
  description: "So navigieren Sie durch das Energie-Dashboard"
}, {
  id: "7LhnBK2pY9o",
  title: "Erweiterte Funktionen",
  description: "Entdecken Sie alle Features der Plattform"
}];
const VideoCarousel = () => {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  useEffect(() => {
    if (!api) {
      return;
    }
    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);
    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);
  const scrollToSlide = (index: number) => {
    api?.scrollTo(index);
  };
  return <section className="py-24 bg-gradient-to-br from-background via-background to-primary/5">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">Tutorials</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Entdecken Sie in unseren Video-Tutorials, wie einfach und effektiv unsere Plattform funktioniert
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <Carousel className="w-full" setApi={setApi}>
            <CarouselContent>
              {videoData.map((video, index) => <CarouselItem key={index}>
                  <div className="p-2">
                    <div className="bg-card rounded-2xl shadow-soft overflow-hidden">
                      <div className="aspect-video">
                        <iframe src={`https://www.youtube.com/embed/${video.id}`} title={video.title} allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowFullScreen className="w-full h-full rounded-t-2xl" />
                      </div>
                      <div className="p-6">
                        <h3 className="text-xl font-semibold text-foreground mb-3">
                          {video.title}
                        </h3>
                        <p className="text-muted-foreground">
                          {video.description}
                        </p>
                      </div>
                    </div>
                  </div>
                </CarouselItem>)}
            </CarouselContent>
            <CarouselPrevious className="left-4" />
            <CarouselNext className="right-4" />
          </Carousel>
        </div>

        {/* Interactive indicator dots */}
        <div className="flex justify-center gap-2 mt-8">
          {videoData.map((_, index) => <button key={index} onClick={() => scrollToSlide(index)} className={`w-3 h-3 rounded-full transition-all duration-200 ${current === index + 1 ? "bg-primary scale-110" : "bg-muted-foreground/30 hover:bg-muted-foreground/50"}`} aria-label={`Zu Video ${index + 1} wechseln`} />)}
        </div>
      </div>
    </section>;
};
export default VideoCarousel;