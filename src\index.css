@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 98%;
    --foreground: 220 26% 8%;

    --card: 0 0% 100%;
    --card-foreground: 220 26% 8%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 26% 8%;

    --primary: 142 86% 45%;
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 96%;
    --secondary-foreground: 220 26% 8%;

    --muted: 0 0% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 142 86% 45%;
    --accent-foreground: 0 0% 100%;
    --accent-secondary: 11 93% 66%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 0 0% 100%;
    --ring: 142 86% 45%;
    
    --gradient-hero: linear-gradient(135deg, hsl(0 0% 98%), hsl(0 0% 100%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(0 0% 96%));
    --shadow-glow: 0 0 40px hsl(142 86% 45% / 0.1);
    --shadow-soft: 0 10px 40px -15px hsl(0 0% 0% / 0.1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
    
    --nav-background: 221 17% 22%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 142 86% 45%;
    --primary-foreground: 222.2 84% 4.9%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 142 86% 45%;
    --accent-foreground: 222.2 84% 4.9%;
    --accent-secondary: 11 93% 66%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 142 86% 45%;
    
    --gradient-hero: linear-gradient(135deg, hsl(222.2 84% 4.9%), hsl(217.2 32.6% 17.5%));
    --gradient-card: linear-gradient(145deg, hsl(222.2 84% 4.9%), hsl(217.2 32.6% 17.5%));
    --shadow-glow: 0 0 40px hsl(142 86% 45% / 0.2);
    --shadow-soft: 0 10px 40px -15px hsl(0 0% 0% / 0.3);
    
    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 142 86% 45%;
    --sidebar-primary-foreground: 222.2 84% 4.9%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 142 86% 45%;
    
    --nav-background: 222.2 84% 4.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}