import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Star, Quote, Shield, Lock, CheckCircle } from "lucide-react";
const testimonial = {
  name: "<PERSON><PERSON> <PERSON>",
  role: "Energieberater & Sachverständiger",
  company: "EnergiePlus Beratung",
  image: "/api/placeholder/64/64",
  rating: 5,
  quote: "SmartEnergy hat unsere Arbeitsweise revolutioniert. Die 60% Zeitersparnis sind real - wir können jetzt doppelt so viele Projekte bearbeiten.",
  highlight: "60% Zeitersparnis"
};
const Testimonials = () => {
  return <section id="testimonials" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="w-fit mx-auto">
            Referenzen
          </Badge>
          <h2 className="text-3xl lg:text-5xl font-bold text-foreground">
            Was unsere Kunden
            <span className="block text-primary">über uns sagen</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Über 500+ Energieberater vertrauen bereits auf SmartEnergy für ihre tägliche Arbeit.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary">100+</div>
            <div className="text-sm text-muted-foreground">Aktive Nutzer</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary">1.000+</div>
            <div className="text-sm text-muted-foreground">4.3/5</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary">4.9/5</div>
            <div className="text-sm text-muted-foreground">Kundenbewertung</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary">60%</div>
            <div className="text-sm text-muted-foreground">Zeitersparnis</div>
          </div>
        </div>

        {/* Single Testimonial */}
        <div className="max-w-4xl mx-auto">
          <Card className="bg-gradient-card border-border shadow-soft">
            <CardContent className="p-8 space-y-6">
              {/* Quote Icon */}
              <Quote className="w-12 h-12 text-primary/20" />
              
              {/* Rating */}
              <div className="flex items-center space-x-1">
                {[...Array(testimonial.rating)].map((_, i) => <Star key={i} className="w-5 h-5 fill-primary text-primary" />)}
              </div>
              
              {/* Quote */}
              <blockquote className="text-xl text-foreground leading-relaxed">
                "{testimonial.quote}"
              </blockquote>
              
              {/* Highlight */}
              <Badge variant="secondary" className="bg-primary/10 text-primary">
                {testimonial.highlight}
              </Badge>
              
              {/* Author */}
              <div className="flex items-center space-x-4 pt-6 border-t border-border">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={testimonial.image} alt={testimonial.name} />
                  <AvatarFallback className="text-lg">
                    {testimonial.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <div className="text-lg font-semibold text-foreground">{testimonial.name}</div>
                  <div className="text-muted-foreground">{testimonial.role}</div>
                  <div className="text-sm text-muted-foreground">{testimonial.company}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Trust Badges */}
        <div className="max-w-4xl mx-auto mt-12">
          <div className="flex flex-col md:flex-row gap-4 md:gap-6 items-center justify-center">
            {/* DSGVO Badge */}
            <div className="inline-flex items-center space-x-3 px-6 py-3 bg-muted/80 rounded-full border border-border/50 hover:bg-muted transition-all duration-300">
              <Shield className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-foreground">DSGVO-konform</span>
            </div>

            {/* SSL Badge */}
            <div className="inline-flex items-center space-x-3 px-6 py-3 bg-muted/80 rounded-full border border-border/50 hover:bg-muted transition-all duration-300">
              <Lock className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-foreground">SSL-verschlüsselt</span>
            </div>

            {/* DIN Badge */}
            <div className="inline-flex items-center space-x-3 px-6 py-3 bg-muted/80 rounded-full border border-border/50 hover:bg-muted transition-all duration-300">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <span className="text-sm font-medium text-foreground">DIN 18599</span>
            </div>
          </div>
        </div>
      </div>
    </section>;
};
export default Testimonials;