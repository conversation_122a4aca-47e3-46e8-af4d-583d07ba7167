import { Badge } from "@/components/ui/badge";
import bfsz<PERSON>ogo from "@/assets/bfsz-logo.webp";
import DENE<PERSON><PERSON><PERSON> from "@/assets/DENEFF_Logo.svg";
import SAFLogo from "@/assets/SAF_logo.png";

const networks = [
  {
    name: "FuE BSFZ 2024",
    image: bfsz<PERSON><PERSON>,
    description: "Siegel der Bundesförderung für Forschung und Entwicklung, welches die Innovationskompetenz fürs Forschen und Entwickeln bestätigt."
  },
  {
    name: "DENEFF",
    image: DENEFFLogo,
    description: "Mitglied der Deutschen Unternehmensinitiative Energieeffizienz e.V."
  },
  {
    name: "BRANCHENZENTRUM AUSBAU und FASSADE",
    image: SAFLogo,
    description: "Mitglied im Fachverband der Stuckateure"
  }
];

const Networks = () => {
  return (
    <section id="networks" className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="w-fit mx-auto">
            Zertifizierungen
          </Badge>
          <h2 className="text-3xl lg:text-5xl font-bold text-foreground">
            Unsere
            <span className="block text-primary">Netzwerke</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            SmartEnergy ist Teil führender Innovationsnetzwerke und erfüllt höchste Standards für Forschung und Entwicklung.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          {networks.map((network, index) => (
            <div key={index} className="text-center space-y-4">
              <div className="w-32 h-32 mx-auto bg-background rounded-full p-4 shadow-soft flex items-center justify-center">
                <img 
                  src={network.image} 
                  alt={network.name}
                  className="w-full h-full object-contain"
                />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-foreground mb-2">{network.name}</h3>
                <p className="text-sm text-muted-foreground">{network.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Networks;