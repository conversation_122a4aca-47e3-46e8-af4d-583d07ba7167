import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";

interface RegistrationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const RegistrationDialog = ({ open, onOpenChange }: RegistrationDialogProps) => {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    company: "",
    usePurpose: [] as string[],
    termsAccepted: false,
    privacyAccepted: false,
    marketingConsent: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleInputChange = (field: string, value: string | boolean | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleUsePurposeChange = (purpose: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      usePurpose: checked 
        ? [...prev.usePurpose, purpose]
        : prev.usePurpose.filter(p => p !== purpose)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.termsAccepted || !formData.privacyAccepted) {
      toast({
        title: "Fehler",
        description: "Bitte akzeptieren Sie die AGB und Datenschutzerklärung.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    toast({
      title: "Registrierung erfolgreich!",
      description: "Wir haben Ihnen eine E-Mail mit Ihren Zugangsdaten gesendet.",
    });
    
    setIsSubmitting(false);
    onOpenChange(false);
    
    // Reset form
    setFormData({
      firstName: "",
      lastName: "",
      email: "",
      company: "",
      usePurpose: [],
      termsAccepted: false,
      privacyAccepted: false,
      marketingConsent: false
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">Kostenlose 14-Tage Testversion starten</DialogTitle>
          <DialogDescription>
            Testen Sie SmartEnergy 14 Tage kostenlos. Keine Kreditkarte erforderlich.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">Vorname *</Label>
              <Input
                id="firstName"
                value={formData.firstName}
                onChange={(e) => handleInputChange("firstName", e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">Nachname *</Label>
              <Input
                id="lastName"
                value={formData.lastName}
                onChange={(e) => handleInputChange("lastName", e.target.value)}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email">E-Mail-Adresse *</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="company">Unternehmensname (optional)</Label>
            <Input
              id="company"
              value={formData.company}
              onChange={(e) => handleInputChange("company", e.target.value)}
            />
          </div>

          <div className="space-y-3">
            <Label>Gewünschter Verwendungszweck</Label>
            <div className="grid grid-cols-1 gap-3">
              {[
                "Energieberatung",
                "Heizlastberechnung", 
                "Baukostenschätzung",
                "Baubegleitung",
                "Sonstige"
              ].map((purpose) => (
                <div key={purpose} className="flex items-center space-x-3">
                  <Checkbox
                    id={purpose}
                    checked={formData.usePurpose.includes(purpose)}
                    onCheckedChange={(checked) => handleUsePurposeChange(purpose, !!checked)}
                  />
                  <Label htmlFor={purpose} className="cursor-pointer text-sm">
                    {purpose}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4 pt-4 border-t">
            <div className="flex items-start space-x-3">
              <Checkbox
                id="terms"
                checked={formData.termsAccepted}
                onCheckedChange={(checked) => handleInputChange("termsAccepted", !!checked)}
                required
              />
              <div className="text-sm leading-relaxed">
                <Label htmlFor="terms" className="cursor-pointer">
                  Ich akzeptiere die{" "}
                  <a href="/agb" target="_blank" className="text-primary hover:underline">
                    Allgemeinen Geschäftsbedingungen
                  </a>{" "}
                  *
                </Label>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <Checkbox
                id="privacy"
                checked={formData.privacyAccepted}
                onCheckedChange={(checked) => handleInputChange("privacyAccepted", !!checked)}
                required
              />
              <div className="text-sm leading-relaxed">
                <Label htmlFor="privacy" className="cursor-pointer">
                  Ich akzeptiere die{" "}
                  <a href="/datenschutz" target="_blank" className="text-primary hover:underline">
                    Datenschutzerklärung
                  </a>{" "}
                  *
                </Label>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <Checkbox
                id="marketing"
                checked={formData.marketingConsent}
                onCheckedChange={(checked) => handleInputChange("marketingConsent", !!checked)}
              />
              <div className="text-sm leading-relaxed">
                <Label htmlFor="marketing" className="cursor-pointer">
                  Ich möchte über Produktneuigkeiten und Updates per E-Mail informiert werden (optional)
                </Label>
              </div>
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1"
            >
              Abbrechen
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.termsAccepted || !formData.privacyAccepted}
              className="flex-1 bg-primary hover:bg-primary/90"
            >
              {isSubmitting ? "Wird verarbeitet..." : "Kostenlose Testversion starten"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};