import { useLanguage } from "@/contexts/LanguageContext";
import { Link } from "react-router-dom";
import { Mail, Phone, MapPin, Globe } from "lucide-react";

const Footer = () => {
  const { t, toggleLanguage } = useLanguage();

  return (
    <footer className="bg-background border-t">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
          {/* Logo and Description */}
          <div className="md:col-span-2 space-y-4">
            <div className="flex items-center space-x-2">
              <img 
                src="https://storage.googleapis.com/gpt-engineer-file-uploads/yOUqcMp304PmrAkcxQNuCqBx1xi1/uploads/1757588137165-favicon.svg" 
                alt="doorbit Logo" 
                className="h-8 w-auto"
              />
            </div>
            <p className="text-sm text-muted-foreground max-w-xs">
              Moderne Software-Lösungen für Energieberatung und darüber hinaus. 
              Digitalisieren Sie Ihre Energieberatung mit KI-gestützten Tools.
            </p>
          </div>
          
          {/* Unternehmen */}
          <div className="space-y-4">
            <h3 className="font-semibold text-foreground">Unternehmen</h3>
            <div className="space-y-2 text-sm">
              <button 
                onClick={() => {/* navigate to features */}} 
                className="block text-muted-foreground hover:text-foreground transition-colors text-left"
              >
                Features
              </button>
              <button 
                onClick={() => {/* navigate to pricing */}} 
                className="block text-muted-foreground hover:text-foreground transition-colors text-left"
              >
                Preise
              </button>
              <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Über uns</a>
            </div>
          </div>
          
          {/* Support */}
          <div className="space-y-4">
            <h3 className="font-semibold text-foreground">Support</h3>
            <div className="space-y-2 text-sm">
              <button 
                onClick={() => {/* navigate to faq */}} 
                className="block text-muted-foreground hover:text-foreground transition-colors text-left"
              >
                FAQ
              </button>
              <a href="https://docs.doorbit.com" target="_blank" rel="noopener noreferrer" className="block text-muted-foreground hover:text-foreground transition-colors">
                Dokumentation
              </a>
              <a href="#" className="block text-muted-foreground hover:text-foreground transition-colors">Kontakt</a>
            </div>
          </div>
          
          {/* Kontakt */}
          <div className="space-y-4">
            <h3 className="font-semibold text-foreground">Kontakt</h3>
            <div className="space-y-2 text-sm">
              <a href="mailto:<EMAIL>" className="flex items-center text-muted-foreground hover:text-foreground transition-colors">
                <Mail className="w-4 h-4 mr-2 text-primary" />
                <EMAIL>
              </a>
              <a href="tel:+4940307525999" className="flex items-center text-muted-foreground hover:text-foreground transition-colors">
                <Phone className="w-4 h-4 mr-2 text-primary" />
                +49 (0) 40 30752599
              </a>
              <div className="flex items-start text-muted-foreground">
                <MapPin className="w-4 h-4 mr-2 mt-0.5 text-primary" />
                <div>
                  Uckermarkweg 12d<br />
                  22415 Hamburg, Deutschland
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Bottom Section */}
        <div className="border-t mt-8 pt-8 flex flex-col md:flex-row justify-between items-center text-sm text-muted-foreground">
          <p>&copy; 2025 doorbit GmbH. Alle Rechte vorbehalten.</p>
          
          <div className="flex items-center space-x-6 mt-4 md:mt-0">
            <a href="#" className="hover:text-foreground transition-colors">Datenschutz</a>
            <Link to="/agb" className="hover:text-foreground transition-colors">AGB</Link>
            <Link to="/impressum" className="hover:text-foreground transition-colors">Impressum</Link>
            <button 
              onClick={toggleLanguage}
              className="flex items-center hover:text-foreground transition-colors"
            >
              <Globe className="w-4 h-4 mr-1 text-primary" />
              English
            </button>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
