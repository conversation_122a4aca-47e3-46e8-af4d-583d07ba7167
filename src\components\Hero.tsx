import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON>, Z<PERSON>, Clock, Users, Play } from "lucide-react";
import HeroCarousel from "./HeroCarousel";
const Hero = () => {
  return <section className="pt-16 min-h-screen lg:min-h-0 bg-gradient-hero relative overflow-hidden">
      <div className="container mx-auto px-4 py-12 lg:py-20">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">
          
          {/* Content */}
          <div className="space-y-8">
            <Badge variant="secondary" className="w-fit">
              <Zap className="w-4 h-4 mr-2" />
              Neue Version verfügbar
            </Badge>
            
            <div className="space-y-6">
              <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
                digitale
                <span className="block text-primary"> Gebäudeerfassung</span>
                <span className="block">für Experten</span>
              </h1>
              
              <p className="text-xl text-muted-foreground max-w-xl">
                Gestalte deine Energieberatung, Baubegleitung, Heizlastberechnungen & vieles mehr <span className="text-primary font-semibold">smarter, kollaborativer und einfacher</span>
              </p>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-primary" />
                <span className="text-sm text-muted-foreground">21 Tage kostenlos testen</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-5 h-5 text-primary" />
                <span className="text-sm text-muted-foreground">60% Zeitersparnis</span>
              </div>
              <div className="flex items-center space-x-2">
                <Play className="w-5 h-5 text-primary" />
                <span className="text-sm text-muted-foreground">sofort loslegen</span>
              </div>
            </div>

            {/* CTA */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="bg-primary hover:bg-primary/90 shadow-glow">
                Kostenlos testen
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button variant="outline" size="lg">
                Demo anfordern
              </Button>
            </div>

            {/* Trust */}
            <div className="text-sm text-muted-foreground">
              Bereits von über <span className="text-primary font-semibold">100+ Kunden</span> vertraut
            </div>
          </div>

          {/* Image Carousel */}
          <div className="relative">
            <HeroCarousel />
          </div>
        </div>
      </div>
    </section>;
};
export default Hero;
