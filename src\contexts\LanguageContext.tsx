import React, { createContext, useContext, useState, useEffect } from 'react';

type Language = 'de' | 'en';

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  toggleLanguage: () => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const translations = {
  de: {
    // Navigation
    'nav.features': 'Features',
    'nav.pricing': 'Preise',
    'nav.testimonials': 'Referenzen',
    'nav.faq': 'FAQ',
    'nav.documentation': 'Dokumentation',
    'nav.contact': 'Kontakt',
    'nav.login': 'Anmelden',
    'nav.trial': 'Kostenlos testen',
    
    // Hero
    'hero.title': 'Revolutioniere deine Energieberatung',
    'hero.subtitle': 'Digitale Lösungen für effiziente Gebäudeanalyse und -optimierung',
    'hero.cta': 'Jetzt starten',
    'hero.demo': 'Demo ansehen',
    
    // Features
    'features.title': 'Unsere Kernfunktionen',
    'features.subtitle': 'Alles was du für eine professionelle Energieberatung benötigst',
    'features.scanning.title': 'Mobile Gebäudeerfassung',
    'features.scanning.description': 'Erfasse Gebäudedaten schnell und präzise mit unserem mobilen Scanner',
    'features.analysis.title': 'KI-gestützte Analyse',
    'features.analysis.description': 'Automatische Auswertung und Empfehlungen durch künstliche Intelligenz',
    'features.dashboard.title': 'Energie-Dashboard',
    'features.dashboard.description': 'Übersichtliche Darstellung aller Energiekennwerte und Trends',
    'features.documentation.title': 'Foto-Dokumentation',
    'features.documentation.description': 'Vollständige Dokumentation mit integrierten Foto- und Notizfunktionen',
    'features.export.title': 'Datenexport',
    'features.export.description': 'Professionelle Berichte und Exportfunktionen für verschiedene Formate',
    
    // Pricing
    'pricing.title': 'Preise',
    'pricing.subtitle': 'Wählen Sie den passenden Plan für Ihre Bedürfnisse',
    
    // FAQ
    'faq.title': 'Häufig gestellte Fragen',
    'faq.subtitle': 'Antworten auf die wichtigsten Fragen',
    
    // Testimonials
    'testimonials.title': 'Das sagen unsere Kunden',
    'testimonials.subtitle': 'Vertrauen Sie auf die Erfahrungen erfolgreicher Energieberater',
    
    // CTA
    'cta.title': 'Bereit für die Zukunft der Energieberatung?',
    'cta.subtitle': 'Starten Sie noch heute mit der Digitalisierung Ihrer Energieberatung',
    'cta.button': 'Kostenlos testen',
     
    // Footer
    'footer.language': 'English',
    'footer.rights': 'Alle Rechte vorbehalten.',
    'footer.company': 'Unternehmen',
    'footer.about': 'Über uns',
    'footer.careers': 'Karriere',
    'footer.press': 'Presse',
    'footer.support': 'Support',
    'footer.help': 'Hilfe',
    'footer.status': 'Status',
    'footer.legal': 'Rechtliches',
    'footer.privacy': 'Datenschutz',
    'footer.terms': 'AGB',
    'footer.imprint': 'Impressum'
  },
  en: {
    // Navigation
    'nav.features': 'Features',
    'nav.pricing': 'Pricing',
    'nav.testimonials': 'References',
    'nav.faq': 'FAQ',
    'nav.documentation': 'Documentation',
    'nav.contact': 'Contact',
    'nav.login': 'Sign In',
    'nav.trial': 'Free Trial',
    
    // Hero
    'hero.title': 'Revolutionize Your Energy Consulting',
    'hero.subtitle': 'Digital solutions for efficient building analysis and optimization',
    'hero.cta': 'Get Started',
    'hero.demo': 'Watch Demo',
    
    // Features
    'features.title': 'Our Core Features',
    'features.subtitle': 'Everything you need for professional energy consulting',
    'features.scanning.title': 'Mobile Building Scanning',
    'features.scanning.description': 'Capture building data quickly and precisely with our mobile scanner',
    'features.analysis.title': 'AI-Powered Analysis',
    'features.analysis.description': 'Automatic evaluation and recommendations through artificial intelligence',
    'features.dashboard.title': 'Energy Dashboard',
    'features.dashboard.description': 'Clear visualization of all energy metrics and trends',
    'features.documentation.title': 'Photo Documentation',
    'features.documentation.description': 'Complete documentation with integrated photo and note functions',
    'features.export.title': 'Data Export',
    'features.export.description': 'Professional reports and export functions for various formats',
    
    // Pricing
    'pricing.title': 'Pricing',
    'pricing.subtitle': 'Choose the right plan for your needs',
    
    // FAQ
    'faq.title': 'Frequently Asked Questions',
    'faq.subtitle': 'Answers to the most important questions',
    
    // Testimonials
    'testimonials.title': 'What Our Customers Say',
    'testimonials.subtitle': 'Trust the experiences of successful energy consultants',
    
    // CTA
    'cta.title': 'Ready for the Future of Energy Consulting?',
    'cta.subtitle': 'Start digitizing your energy consulting today',
    'cta.button': 'Start Free Trial',
    
    // Footer
    'footer.language': 'Deutsch',
    'footer.rights': 'All rights reserved.',
    'footer.company': 'Company',
    'footer.about': 'About',
    'footer.careers': 'Careers',
    'footer.press': 'Press',
    'footer.support': 'Support',
    'footer.help': 'Help',
    'footer.status': 'Status',
    'footer.legal': 'Legal',
    'footer.privacy': 'Privacy',
    'footer.terms': 'Terms',
    'footer.imprint': 'Imprint'
  }
};

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>('de');

  useEffect(() => {
    const savedLanguage = localStorage.getItem('language') as Language;
    if (savedLanguage && (savedLanguage === 'de' || savedLanguage === 'en')) {
      setLanguage(savedLanguage);
    }
  }, []);

  const handleSetLanguage = (newLanguage: Language) => {
    setLanguage(newLanguage);
    localStorage.setItem('language', newLanguage);
  };

  const toggleLanguage = () => {
    const newLanguage = language === 'de' ? 'en' : 'de';
    handleSetLanguage(newLanguage);
  };

  const t = (key: string): string => {
    return translations[language][key] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, toggleLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}