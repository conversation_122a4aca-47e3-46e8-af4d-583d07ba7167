import { useState, useEffect } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigation } from "@/hooks/useNavigation";
import { useLocation } from 'react-router-dom';

const ScrollNavigator = () => {
  const { navigateToSection } = useNavigation();
  const location = useLocation();
  
  // Only show ScrollNavigator on homepage
  if (location.pathname !== '/') {
    return null;
  }

  const [currentSection, setCurrentSection] = useState(0);
  
  const sections = [
    { id: "hero", name: "Start" },
    { id: "features", name: "Features" },
    { id: "videos", name: "Videos" },
    { id: "pricing", name: "Pricing" },
    { id: "testimonials", name: "Testimonials" },
    { id: "networks", name: "Networks" },
    { id: "faq", name: "FAQ" },
    { id: "contact", name: "Contact" }
  ];

  const scrollToNextSection = () => {
    const isLastSection = currentSection === sections.length - 1;
    
    if (isLastSection) {
      window.scrollTo({ top: 0, behavior: "smooth" });
    } else {
      const nextIndex = currentSection + 1;
      const nextSectionId = sections[nextIndex].id;
      navigateToSection(nextSectionId);
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + window.innerHeight / 2;
      
      sections.forEach((section, index) => {
        const element = document.getElementById(section.id);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setCurrentSection(index);
          }
        }
      });
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll(); // Initial check
    
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div className="fixed bottom-8 right-8 z-50">
      <Button
        onClick={scrollToNextSection}
        size="sm"
        className="w-12 h-12 rounded-full bg-background/80 hover:bg-background/90 shadow-glow border-2 border-border backdrop-blur-sm transition-all duration-300 hover:scale-110 group"
        aria-label="Zur nächsten Section"
      >
        <div className="flex flex-col items-center -space-y-1">
          {currentSection === sections.length - 1 ? (
            <>
              <ChevronUp className="w-3 h-3 text-green-500 group-hover:animate-bounce animation-delay-0" />
              <ChevronUp className="w-3 h-3 text-green-500 group-hover:animate-bounce animation-delay-75" />
            </>
          ) : (
            <>
              <ChevronDown className="w-3 h-3 text-green-500 group-hover:animate-bounce animation-delay-0" />
              <ChevronDown className="w-3 h-3 text-green-500 group-hover:animate-bounce animation-delay-75" />
            </>
          )}
        </div>
      </Button>
      
      {/* Section Indicator */}
      <div className="absolute -left-24 top-1/2 -translate-y-1/2 bg-background/80 backdrop-blur-sm rounded-full px-3 py-1 text-xs font-medium text-foreground border border-border opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        {sections[currentSection]?.name}
      </div>
    </div>
  );
};

export default ScrollNavigator;
