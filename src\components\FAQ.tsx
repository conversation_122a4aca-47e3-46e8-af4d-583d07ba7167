import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

const faqs = [
  {
    question: "Wie lange dauert die kostenlose Testphase?",
    answer: "Die kostenlose Testphase dauert 14 Tage und Sie haben Zugang zu allen Standard-Features. Es sind keine Kreditkarteninformationen erforderlich."
  },
  {
    question: "Welche Geräte werden für LiDAR-Scans unterstützt?",
    answer: "SmartEnergy unterstützt alle aktuellen iPhones und iPads mit LiDAR-Sensor (iPhone 12 Pro und neuer, iPad Pro 2020 und neuer). Zusätzlich arbeiten wir mit professionellen LiDAR-Scannern verschiedener Hersteller."
  },
  {
    question: "Können bestehende Projekte importiert werden?",
    answer: "Ja, SmartEnergy unterstützt den Import aus verschiedenen CAD-Formaten (DWG, DXF, IFC) sowie aus gängigen Energieberatungs-Tools. Unser Support-Team hilft Ihnen gerne beim Migrationsprozess."
  },
  {
    question: "Wie funktioniert die Team-Zusammenarbeit?",
    answer: "Teams können gleichzeitig an Projekten arbeiten, Kommentare hinterlassen, Aufgaben zuweisen und den Projektfortschritt verfolgen. Alle Änderungen werden in Echtzeit synchronisiert."
  },
  {
    question: "Welche Export-Formate werden unterstützt?",
    answer: "SmartEnergy exportiert in alle gängigen Formate: PDF-Berichte, DWG/DXF für CAD, IFC für BIM, CSV für Datenanalyse und XML für die Übertragung an Förderstellen."
  },
  {
    question: "Ist SmartEnergy DSGVO-konform?",
    answer: "Ja, SmartEnergy ist vollständig DSGVO-konform. Alle Daten werden auf deutschen Servern gespeichert und wir haben umfassende Datenschutzmaßnahmen implementiert."
  },
  {
    question: "Gibt es Rabatte für Bildungseinrichtungen?",
    answer: "Ja, wir bieten spezielle Konditionen für Universitäten, Fachhochschulen und Bildungseinrichtungen. Kontaktieren Sie uns für ein individuelles Angebot."
  },
  {
    question: "Wie erfolgt der Support?",
    answer: "Standard-Kunden erhalten E-Mail-Support mit Antwortzeiten unter 24 Stunden. Enterprise-Kunden haben Zugang zu Priority-Support, Telefon-Support und einem dedizierten Account Manager."
  },
  {
    question: "Können individuelle Anpassungen vorgenommen werden?",
    answer: "Enterprise-Kunden können Workflows anpassen, eigene Berechnungsmodule integrieren und White-Label-Lösungen nutzen. Sprechen Sie uns für individuelle Anforderungen an."
  },
  {
    question: "Funktioniert SmartEnergy offline?",
    answer: "Die mobile App funktioniert für LiDAR-Scans und Datenaufnahme offline. Für die Bearbeitung im CAD-Studio und Team-Funktionen ist eine Internetverbindung erforderlich."
  }
];

const FAQ = () => {
  return (
    <section id="faq" className="py-20 bg-secondary/20">
      <div className="container mx-auto px-4">
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="w-fit mx-auto">
            FAQ
          </Badge>
          <h2 className="text-3xl lg:text-5xl font-bold text-foreground">
            Häufig gestellte
            <span className="block text-primary">Fragen</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Hier finden Sie Antworten auf die wichtigsten Fragen zu SmartEnergy. 
            Sollten Sie weitere Fragen haben, kontaktieren Sie uns gerne.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <Accordion type="single" collapsible className="space-y-4">
            {faqs.map((faq, index) => (
              <AccordionItem 
                key={index} 
                value={`item-${index}`}
                className="bg-gradient-card border border-border rounded-lg px-6 data-[state=open]:shadow-glow transition-all duration-300"
              >
                <AccordionTrigger className="text-left font-semibold text-foreground hover:text-primary transition-colors">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-muted-foreground leading-relaxed">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  );
};

export default FAQ;