import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import { RegistrationDialog } from "./RegistrationDialog";
import { ThemeToggle } from "./ThemeToggle";
import { useLanguage } from "@/contexts/LanguageContext";
import dbtLogo from "@/assets/dbt_logo_white.svg";
import { useNavigation } from "@/hooks/useNavigation";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isRegistrationOpen, setIsRegistrationOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { t } = useLanguage();
  const { navigateToSection } = useNavigation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <>
      {/* Main Header - Initially transparent, hidden when scrolled on desktop, always visible on mobile */}
      <header className={`fixed top-0 w-full z-50 transition-all duration-300 ${
        isScrolled ? 'md:opacity-0 md:pointer-events-none opacity-100' : 'opacity-100'
      } bg-nav-background`}>
        <div className="h-px bg-border w-full absolute bottom-0"></div>
        <nav className="container mx-auto px-4 h-10 md:h-16 flex items-center justify-between">
          {/* Mobile Layout: Burger - Logo - (Theme + Login) */}
          <div className="md:hidden flex items-center justify-between w-full">
            {/* Mobile Menu Button - Left */}
            <button
              className="text-white"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </button>
            
            {/* Mobile Logo - Center */}
            <div className="flex items-center">
              <button
                onClick={() => navigateToSection('hero')}
                className="flex items-center hover:opacity-80 transition-opacity"
              >
                <img
                  src={dbtLogo}
                  alt="DBT Logo"
                  className="h-6 w-auto"
                />
              </button>
            </div>
            
            {/* Mobile Theme Toggle + Login Button - Right */}
            <div className="flex items-center space-x-2">
              <ThemeToggle />
              <Button variant="ghost" size="sm" className="text-xs px-2 py-1 h-7 text-white/90 hover:text-accent-secondary hover:bg-accent-secondary/10">
                {t('nav.login')}
              </Button>
            </div>
          </div>

          {/* Desktop Logo - Left */}
          <div className="hidden md:flex items-center space-x-2">
            <button
              onClick={() => navigateToSection('hero')}
              className="flex items-center hover:opacity-80 transition-opacity"
            >
              <img
                src={dbtLogo}
                alt="DBT Logo"
                className="h-8 w-auto"
              />
            </button>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <button 
              onClick={() => navigateToSection('features')} 
              className="text-white/80 hover:text-white transition-colors"
            >
              {t('nav.features')}
            </button>
            <button 
              onClick={() => navigateToSection('pricing')} 
              className="text-white/80 hover:text-white transition-colors"
            >
              {t('nav.pricing')}
            </button>
            <button 
              onClick={() => navigateToSection('testimonials')} 
              className="text-white/80 hover:text-white transition-colors"
            >
              {t('nav.testimonials')}
            </button>
            <button 
              onClick={() => navigateToSection('faq')} 
              className="text-white/80 hover:text-white transition-colors"
            >
              {t('nav.faq')}
            </button>
            <a href="https://docs.doorbit.com" target="_blank" rel="noopener noreferrer" className="text-white/80 hover:text-white transition-colors">
              {t('nav.documentation')}
            </a>
            <button 
              onClick={() => navigateToSection('contact')} 
              className="text-white/80 hover:text-white transition-colors"
            >
              {t('nav.contact')}
            </button>
          </div>

          {/* Desktop CTA */}
          <div className="hidden md:flex items-center space-x-4">
            <ThemeToggle />
            <Button variant="ghost" size="sm" className="text-white/90 hover:text-accent-secondary hover:bg-accent-secondary/10">
              {t('nav.login')}
            </Button>
            <Button size="sm" className="bg-primary hover:bg-primary/90" onClick={() => setIsRegistrationOpen(true)}>
              {t('nav.trial')}
            </Button>
          </div>
        </nav>
      </header>

      {/* Compact Sticky Navigation - Appears when scrolled, desktop only */}
      <div className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-50 transition-all duration-300 hidden md:block ${
        isScrolled ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-4 pointer-events-none'
      }`}>
        <nav className="bg-nav-background/95 backdrop-blur-lg border border-border rounded-full px-6 py-2 shadow-soft">
          <div className="flex items-center space-x-6">
            {/* Compact Logo */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => navigateToSection('hero')}
                className="flex items-center hover:opacity-80 transition-opacity"
              >
                <img
                  src={dbtLogo}
                  alt="DBT Logo"
                  className="h-6 w-auto"
                />
              </button>
            </div>

            {/* Compact Navigation Links */}
            <div className="hidden md:flex items-center space-x-4 text-sm">
              <button 
                onClick={() => navigateToSection('features')} 
                className="text-white/80 hover:text-white transition-colors"
              >
                {t('nav.features')}
              </button>
              <button 
                onClick={() => navigateToSection('pricing')} 
                className="text-white/80 hover:text-white transition-colors"
              >
                {t('nav.pricing')}
              </button>
              <button 
                onClick={() => navigateToSection('faq')} 
                className="text-white/80 hover:text-white transition-colors"
              >
                {t('nav.faq')}
              </button>
              <a href="https://docs.doorbit.com" target="_blank" rel="noopener noreferrer" className="text-white/80 hover:text-white transition-colors">
                {t('nav.documentation')}
              </a>
              <button 
                onClick={() => navigateToSection('contact')} 
                className="text-white/80 hover:text-white transition-colors"
              >
                {t('nav.contact')}
              </button>
            </div>

            {/* Compact CTA Buttons */}
            <div className="flex items-center space-x-2">
              <ThemeToggle />
              <Button variant="ghost" size="sm" className="text-xs px-3 py-1 h-8 text-white/90 hover:text-accent-secondary hover:bg-accent-secondary/10 border-accent-secondary/20">
                {t('nav.login')}
              </Button>
              <Button size="sm" className="text-xs px-3 py-1 h-8 bg-primary hover:bg-primary/90" onClick={() => setIsRegistrationOpen(true)}>
                {t('nav.trial')}
              </Button>
            </div>
          </div>
        </nav>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden fixed top-10 left-0 right-0 z-40 bg-background border-b border-border">
          <div className="container mx-auto px-4 py-4 space-y-4">
            <a href="#features" className="block text-muted-foreground hover:text-foreground transition-colors" onClick={() => setIsMenuOpen(false)}>
              {t('nav.features')}
            </a>
            <a href="#pricing" className="block text-muted-foreground hover:text-foreground transition-colors" onClick={() => setIsMenuOpen(false)}>
              {t('nav.pricing')}
            </a>
            <a href="#testimonials" className="block text-muted-foreground hover:text-foreground transition-colors" onClick={() => setIsMenuOpen(false)}>
              {t('nav.testimonials')}
            </a>
            <a href="#faq" className="block text-muted-foreground hover:text-foreground transition-colors" onClick={() => setIsMenuOpen(false)}>
              {t('nav.faq')}
            </a>
            <a href="https://docs.doorbit.com" target="_blank" rel="noopener noreferrer" className="block text-muted-foreground hover:text-foreground transition-colors" onClick={() => setIsMenuOpen(false)}>
              {t('nav.documentation')}
            </a>
            <a href="#contact" className="block text-muted-foreground hover:text-foreground transition-colors" onClick={() => setIsMenuOpen(false)}>
              {t('nav.contact')}
            </a>
            <div className="pt-4 space-y-2">
              <Button variant="ghost" size="sm" className="w-full text-muted-foreground hover:text-accent-secondary hover:bg-accent-secondary/10">
                {t('nav.login')}
              </Button>
              <Button size="sm" className="w-full bg-primary hover:bg-primary/90" onClick={() => setIsRegistrationOpen(true)}>
                {t('nav.trial')}
              </Button>
            </div>
          </div>
        </div>
      )}

      <RegistrationDialog 
        open={isRegistrationOpen} 
        onOpenChange={setIsRegistrationOpen} 
      />
    </>
  );
};

export default Header;
