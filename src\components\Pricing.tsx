import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { CheckCircle, Star, Zap, Crown, ArrowRight } from "lucide-react";
import { useState } from "react";

const Pricing = () => {
  const [isYearly, setIsYearly] = useState(true);

  const plans = [
    {
      name: "Standard",
      description: "Perfekt für einzelne Energieberater und kleine Teams",
      monthlyPrice: 89,
      yearlyPrice: 790, // 2 Monate Rabatt
      yearlyDiscount: "2 Monate gratis",
      icon: Zap,
      popular: true,
      features: [
        "Bis zu 3 Benutzer",
        "Unbegrenzte Projekte",
        "LiDAR-Scan & 3D-Modellierung",
        "KI-gestützte Auswertung",
        "Standard Export-Formate",
        "E-Mail Support",
        "Mobile App",
        "Cloud-Speicher (50GB)"
      ],
      cta: "14 Tage kostenlos testen"
    },
    {
      name: "Enterprise",
      description: "Für große Teams und individuelle Anforderungen",
      monthlyPrice: null,
      yearlyPrice: null,
      yearlyDiscount: null,
      icon: Crown,
      popular: false,
      features: [
        "Admin Zugang für Usermanagement",
        "API-Zugang",
        "Anpassbare Workflows dank low code",
        "Individuelle Berechnungsapps",
        "White-Label Lösung", 
        "Priority Support",
        "Dedizierter Account Manager",
        "Custom Export-Formate",
        "SLA Garantie"
      ],
      cta: "Individuelle Beratung"
    }
  ];

  return (
    <section id="pricing" className="py-20 bg-secondary/20">
      <div className="container mx-auto px-4">
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="w-fit mx-auto">
            Preise
          </Badge>
          <h2 className="text-3xl lg:text-5xl font-bold text-foreground">
            Einfache und transparente
            <span className="block text-primary">Preisgestaltung</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Starten Sie mit einer 14-tägigen kostenlosen Testphase. Keine Kreditkarte erforderlich.
          </p>
        </div>

        {/* Toggle */}
        <div className="flex items-center justify-center space-x-4 mb-12">
          <span className={`text-sm ${!isYearly ? 'text-foreground font-medium' : 'text-muted-foreground'}`}>
            Monatlich
          </span>
          <Switch 
            checked={isYearly} 
            onCheckedChange={setIsYearly}
            className="data-[state=checked]:bg-primary"
          />
          <div className="flex items-center space-x-2">
            <span className={`text-sm ${isYearly ? 'text-foreground font-medium' : 'text-muted-foreground'}`}>
              Jährlich
            </span>
            <Badge variant="secondary" className="text-xs bg-primary text-primary-foreground">
              2 Monate gratis
            </Badge>
          </div>
        </div>

        {/* Plans */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {plans.map((plan, index) => (
            <Card 
              key={plan.name} 
              className={`relative bg-gradient-card border-border shadow-soft hover:shadow-glow hover:scale-110 transition-all duration-300 ease-out ${
                plan.popular ? 'ring-2 ring-primary scale-105' : ''
              }`}
            >
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground">
                  <Star className="w-3 h-3 mr-1" />
                  Beliebteste Option
                </Badge>
              )}
              
              <CardHeader className="text-center space-y-4">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                  <plan.icon className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="text-2xl font-bold text-foreground">{plan.name}</CardTitle>
                  <CardDescription className="text-muted-foreground mt-2">
                    {plan.description}
                  </CardDescription>
                </div>
                
                <div className="space-y-2">
                  {plan.monthlyPrice ? (
                    <>
                      <div className="text-4xl font-bold text-foreground">
                        €{isYearly ? Math.round(plan.yearlyPrice! / 12) : plan.monthlyPrice}
                        <span className="text-lg text-muted-foreground font-normal">/Monat</span>
                      </div>
                      {isYearly && plan.yearlyDiscount && (
                        <Badge variant="secondary" className="bg-primary/10 text-primary">
                          {plan.yearlyDiscount}
                        </Badge>
                      )}
                    </>
                  ) : (
                    <div className="text-4xl font-bold text-foreground">
                      Individuell
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center space-x-3">
                      <CheckCircle className="w-5 h-5 text-primary flex-shrink-0" />
                      <span className="text-sm text-foreground">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>

              <CardFooter>
                <Button 
                  size="lg" 
                  className={`w-full ${
                    plan.popular 
                      ? 'bg-primary hover:bg-primary/90 shadow-glow' 
                      : 'bg-accent hover:bg-accent/90 text-accent-foreground shadow-soft'
                  }`}
                >
                  {plan.cta}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>

        {/* Trust indicators */}
        <div className="text-center mt-16 space-y-4">
          <div className="flex items-center justify-center space-x-8 text-sm text-muted-foreground">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-primary" />
              <span>14 Tage kostenlos testen</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-primary" />
              <span>Keine Kreditkarte erforderlich</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4 text-primary" />
              <span>{isYearly ? "2 Monate gratis" : "Jederzeit kündbar"}</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Pricing;