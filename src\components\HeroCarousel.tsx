import { useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import heroImage from "@/assets/karussel_1.jpg";
import energyDashboard from "@/assets/karussel_2.jpg";
import buildingModel from "@/assets/karussel_3.jpg";

const carouselImages = [
  {
    src: heroImage,
    alt: "SmartEnergy Dashboard",
    title: "SmartEnergy Dashboard"
  },
  {
    src: energyDashboard,
    alt: "Energie Dashboard",
    title: "Energie Dashboard"
  },
  {
    src: buildingModel,
    alt: "3D Gebäudemodell",
    title: "3D Gebäudemodell"
  }
];

const HeroCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev + 1) % carouselImages.length);
  };

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev - 1 + carouselImages.length) % carouselImages.length);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  const openModal = () => {
    setIsModalOpen(true);
  };

  return (
    <>
      <div className="relative group">
        {/* Image Container */}
        <div className="relative overflow-hidden rounded-2xl">
          <img 
            src={carouselImages[currentIndex].src}
            alt={carouselImages[currentIndex].alt}
            className="w-full h-auto rounded-2xl shadow-soft transition-transform duration-300 ease-in-out cursor-pointer hover:scale-105"
            onClick={openModal}
          />
          
          {/* Navigation Arrows */}
          <Button
            variant="outline"
            size="icon"
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-background/80 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200"
            onClick={prevSlide}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          
          <Button
            variant="outline"
            size="icon"
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-background/80 backdrop-blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-200"
            onClick={nextSlide}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>

        {/* Dot Indicators */}
        <div className="flex justify-center gap-2 mt-4">
          {carouselImages.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                index === currentIndex 
                  ? "bg-primary" 
                  : "bg-muted-foreground/30 hover:bg-muted-foreground/50"
              }`}
              aria-label={`Zu Bild ${index + 1} wechseln`}
            />
          ))}
        </div>

        {/* Glow effect */}
        <div className="absolute inset-0 bg-primary/20 rounded-2xl blur-3xl scale-105 -z-10"></div>
      </div>

      {/* Image Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-4xl w-full h-auto p-0 bg-transparent border-none [&>button]:bg-background/80 [&>button]:backdrop-blur-sm [&>button]:border [&>button]:text-foreground [&>button]:hover:bg-background/90">
          <img 
            src={carouselImages[currentIndex].src}
            alt={carouselImages[currentIndex].alt}
            className="w-full h-auto rounded-lg"
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default HeroCarousel;
