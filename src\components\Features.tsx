import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  <PERSON>an, 
  Calculator, 
  Brain, 
  Settings, 
  Camera, 
  Download,
  Box,
  Edit,
  Clock,
  Presentation,
  Layers,
  Smile
} from "lucide-react";
import lidar from "@/assets/lidar_3.jpg";
import data from "@/assets/data_1.jpg";
import notes from "@/assets/notes_1.jpg";
import workflow from "@/assets/workflow_1.jpg";
import photoDocumentation from "@/assets/photo-documentation.jpg";
import dataExport from "@/assets/export_3.jpg";

const features = [
  {
    id: "01",
    title: "Intelligente Gebäudeerfassung und -bearbeitung",
    description: "App mit LiDAR-Scan und webbasierten doorbit CAD-Studio zur Erstellung DIN V 18599 konformer 3D Modelle von Bestandsgebäuden.",
    items: [
      { icon: Box, label: "Einfache Erstellung" },
      { icon: Edit, label: "100% editierbar" },
      { icon: Clock, label: "Schnell: unter 1 Min./10m²" }
    ],
    color: "primary",
    image: lidar
  },
  {
    id: "02", 
    title: "Umfassende Massedaten",
    description: "Sofortige Bereitstellung aller relevanten Maße für Räume, Stockwerke und das gesamte Gebäude.",
    items: [
      { icon: Presentation, label: "Übersichtliche Darstellung" },
      { icon: Layers, label: "Detailliert bis auf Bauteilebene" },
      { icon: Smile, label: "Einfache Weitergabe an Partner und Kunden" }
    ],
    color: "secondary",
    image: data
  },
  {
    id: "03",
    title: "Fotodokumentation inkl. Notizen im 3D-Modell",
    description: "Erstellen Sie Notizen und Fotos bei Ihrer Vor-Ort-Aufnahmen direkt im 3D-Modell oder im Nachgang für eine lückenlose und leicht nachvollziehbare Dokumentation.",
    items: [
      { icon: Camera, label: "3D-Integration" },
      { icon: Scan, label: "Tagging von Objekten" },
      { icon: Layers, label: "Strukturierte Ablage" }
    ],
    color: "accent",
    image: notes
  },
  {
    id: "04",
    title: "Individualisierbare Workflows für Enterprise Kunden",
    description: "Passen Sie die Datenaufnahme an Ihre spezifischen Anforderungen und Arbeitsweisen mit unseren low code Tools an.",
    items: [
      { icon: Settings, label: "Anpassbare Workflows" },
      { icon: Calculator, label: "auf Wunsch individuelle Berechnungsmodule" },
      { icon: Download, label: "individuelle Export-Funktionen" }
    ],
    color: "secondary",
    image: workflow
  },
  {
    id: "05",
    title: "Datenfreiheit",
    description: "Exportieren Sie Ihre Daten in gängige Formate für die Weiterverarbeitung in ihre weiterführende Programme.",
    items: [
      { icon: Download, label: "Multiple Formate" },
      { icon: Settings, label: "einfach per Link teilbar" },
      { icon: Smile, label: "Partner-kompatibel" }
    ],
    color: "primary",
    image: dataExport
  }
];

const Features = () => {
  return (
    <section id="features" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="w-fit mx-auto">
            Hauptfunktionen
          </Badge>
          <h2 className="text-3xl lg:text-5xl font-bold text-foreground">
            Alles was Sie für moderne
            <span className="block text-primary">Energieberatung brauchen</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Mit doorbit können Sie viele Anwendungsfälle in der Energieberatung sowie darauf aufbauenden Folgeprojekten abbilden, ob als Einzelkämpfer oder im Teamplay. 
            Auf Wunsch passgenau auf Ihre individuellen Anforderungen zugeschnitten.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {features.map((feature, index) => (
            <Card key={feature.id} className="bg-gradient-card border-border shadow-soft hover:shadow-glow transition-all duration-300 overflow-hidden">
              {/* Feature Image */}
              <div className="h-48 overflow-hidden">
                <img 
                  src={feature.image} 
                  alt={feature.title}
                  className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                />
              </div>
              
              <CardHeader className="space-y-4">
                <div className="flex items-center space-x-4">
                  <Badge variant="outline" className="text-2xl font-bold px-3 py-1">
                    {feature.id}
                  </Badge>
                  <div className="h-px bg-border flex-1"></div>
                </div>
                <CardTitle className="text-xl font-bold text-foreground">
                  {feature.title}
                </CardTitle>
                <CardDescription className="text-muted-foreground">
                  {feature.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  {feature.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="flex flex-col items-center text-center space-y-2 p-4 rounded-lg bg-secondary/50">
                      <item.icon className="w-6 h-6 text-primary" />
                      <span className="text-sm font-medium text-foreground">{item.label}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;
